version: '3.8'

services:
  telegram-bot:
    build: .
    container_name: telegram-channel-bot
    restart: unless-stopped
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - CHANNEL_USERNAME=${CHANNEL_USERNAME}
      - CHANNEL_ID=${CHANNEL_ID}
      - ADMIN_USER_IDS=${ADMIN_USER_IDS}
      - AUTO_APPROVE_REQUESTS=${AUTO_APPROVE_REQUESTS:-true}
      - SEND_WELCOME_MESSAGES=${SEND_WELCOME_MESSAGES:-true}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    env_file:
      - .env
    networks:
      - bot-network

networks:
  bot-network:
    driver: bridge

volumes:
  bot-data:
  bot-logs:
