# Telegram <PERSON><PERSON> Configuration
# Copy this file to .env and fill in your actual values

# Required: Your Telegram Bot Token from @BotFather
TELEGRAM_BOT_TOKEN=**********************************************

# Channel Configuration - Choose ONE of the following setups:

# FOR PUBLIC CHANNELS:
# CHANNEL_USERNAME=your_channel_username
# CHANNEL_ID=

# FOR PRIVATE CHANNELS (recommended):
CHANNEL_INVITE_LINK=https://t.me/+your_invite_link_here
CHANNEL_ID=-1001234567890

# Leave CHANNEL_USERNAME empty for private channels
CHANNEL_USERNAME=

# Optional: Admin user IDs (comma-separated)
# Get your user ID by messaging @userinfobot
ADMIN_USER_IDS=123456789,987654321

# File paths (optional, defaults provided)
DATA_FILE=data/bot_data.json
LOG_FILE=logs/bot.log

# Bot behavior settings (optional, defaults provided)
AUTO_APPROVE_REQUESTS=true
SEND_WELCOME_MESSAGES=true
LOG_LEVEL=INFO
MAX_REQUESTS_PER_MINUTE=30

# Custom messages (optional, defaults provided)
WELCOME_MESSAGE=🎉 Welcome {first_name}! I'm your channel assistant bot...
JOIN_MESSAGE=🎯 Ready to join our channel, {first_name}?...
PRIVATE_CHANNEL_JOIN_MESSAGE=🔐 Welcome to our private channel, {first_name}!...
