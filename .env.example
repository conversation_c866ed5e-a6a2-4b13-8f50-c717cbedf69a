# Telegram Bot Configuration
# Copy this file to .env and fill in your actual values

# Required: Your Telegram Bo<PERSON> Token from @BotFather
TELEGRAM_BOT_TOKEN=**********************************************

# Required: Your channel username (without @)
CHANNEL_USERNAME=your_channel_username

# Optional: Your channel ID (for direct operations)
CHANNEL_ID=

# Optional: Admin user IDs (comma-separated)
# Get your user ID by messaging @userinfobot
ADMIN_USER_IDS=123456789,987654321

# File paths (optional, defaults provided)
DATA_FILE=data/bot_data.json
LOG_FILE=logs/bot.log

# Bot behavior settings (optional, defaults provided)
AUTO_APPROVE_REQUESTS=true
SEND_WELCOME_MESSAGES=true
LOG_LEVEL=INFO
MAX_REQUESTS_PER_MINUTE=30

# Custom messages (optional, defaults provided)
WELCOME_MESSAGE=🎉 Welcome {first_name}! I'm your channel assistant bot...
JOIN_MESSAGE=🎯 Ready to join our channel, {first_name}?...
