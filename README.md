# Telegram Channel Bot 🤖

A comprehensive Python Telegram bot for automatic channel management and join request handling.

## Features ✨

- **Auto-approve join requests** - Automatically accepts all channel join requests
- **Direct channel joining** - Provides easy join links and buttons for users
- **User management** - Tracks and remembers all users who interact with the bot
- **Statistics tracking** - Comprehensive stats on joins, users, and bot activity
- **Admin commands** - Special commands for administrators
- **Secure configuration** - Environment variable support for sensitive data
- **Docker support** - Easy deployment with Docker and Docker Compose
- **Comprehensive logging** - Detailed logs for monitoring and debugging

## Quick Start 🚀

### Prerequisites

- Python 3.8 or higher
- A Telegram Bot Token (get one from [@Bot<PERSON>ather](https://t.me/BotFather))
- Your channel username

### Option 1: Quick Installation

1. **Clone and install:**
   ```bash
   git clone <your-repo-url>
   cd joinjesterbot
   ./scripts/install.sh
   ```

2. **Configure your bot:**
   ```bash
   # Edit .env file with your settings
   nano .env
   ```

3. **Start the bot:**
   ```bash
   ./scripts/start.sh
   ```

### Option 2: Manual Installation

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd joinjesterbot
   ```

2. **Create virtual environment:**
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your bot token and channel info
   ```

5. **Run the bot:**
   ```bash
   python run.py
   ```

### Option 3: Docker Deployment

1. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

2. **Deploy with Docker:**
   ```bash
   ./scripts/deploy.sh
   ```

## Configuration ⚙️

### Environment Variables

Create a `.env` file with the following variables:

**For Private Channels (Recommended):**
```env
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_here
CHANNEL_INVITE_LINK=https://t.me/+your_invite_link_here
CHANNEL_ID=-1001234567890

# Leave empty for private channels
CHANNEL_USERNAME=

# Optional
ADMIN_USER_IDS=123456789,987654321
AUTO_APPROVE_REQUESTS=true
SEND_WELCOME_MESSAGES=true
LOG_LEVEL=INFO
DATA_FILE=data/bot_data.json
LOG_FILE=logs/bot.log
```

**For Public Channels:**
```env
# Required
TELEGRAM_BOT_TOKEN=your_bot_token_here
CHANNEL_USERNAME=your_channel_username

# Optional
CHANNEL_ID=your_channel_id
ADMIN_USER_IDS=123456789,987654321
AUTO_APPROVE_REQUESTS=true
SEND_WELCOME_MESSAGES=true
LOG_LEVEL=INFO
DATA_FILE=data/bot_data.json
LOG_FILE=logs/bot.log
```

### Getting Your Bot Token

1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Send `/newbot` and follow the instructions
3. Copy the bot token to your `.env` file

### For Public Channels

**Getting Your Channel Username:**
- Your channel username is the part after `@` in your channel link
- Example: For `https://t.me/mychannel`, use `mychannel`

### For Private Channels (Recommended)

**Getting Your Invite Link:**
1. Open your private channel in Telegram
2. Tap the channel name → **Invite Links**
3. Create or copy an existing invite link
4. Example: `https://t.me/+xDuit1wdWJtmNzhl`

**Getting Your Channel ID:**
1. Add [@userinfobot](https://t.me/userinfobot) to your channel as admin
2. Send any message in the channel
3. The bot will reply with the channel ID (negative number like `-1001234567890`)
4. Remove the bot after getting the ID

### Getting Admin User IDs

1. Message [@userinfobot](https://t.me/userinfobot) on Telegram
2. Copy your user ID to the `ADMIN_USER_IDS` field

## Bot Commands 📋

### User Commands

- `/start` - Start the bot and get welcome message
- `/help` - Show help information
- `/stats` - Show channel statistics
- `/join` - Get channel join link

### Admin Commands

- `/users` - View user statistics (admin only)
- `/backup` - Create data backup (admin only)

## Project Structure 📁

```
joinjesterbot/
├── bot/                    # Main bot package
│   ├── __init__.py
│   ├── main.py            # Main entry point
│   ├── handlers.py        # Command and event handlers
│   └── database.py        # Data management
├── config/                # Configuration
│   └── settings.py        # Settings and environment variables
├── scripts/               # Deployment scripts
│   ├── install.sh         # Installation script
│   ├── start.sh          # Start script
│   └── deploy.sh         # Docker deployment script
├── data/                  # Data storage (created automatically)
├── logs/                  # Log files (created automatically)
├── tests/                 # Test files
├── .env.example          # Environment variables template
├── requirements.txt      # Python dependencies
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose configuration
├── run.py              # Simple runner script
└── README.md           # This file
```

## Usage Examples 💡

### Setting Up Auto-Approval

**For Private Channels:**
1. Add your bot to your private channel as an administrator
2. Give it permission to "Invite users via link"
3. Set `AUTO_APPROVE_REQUESTS=true` in your `.env` file
4. Configure `CHANNEL_INVITE_LINK` with your private channel invite link
5. The bot will automatically approve all join requests

**For Public Channels:**
1. Add your bot to your public channel as an administrator
2. Give it permission to "Invite users via link"
3. Set `AUTO_APPROVE_REQUESTS=true` in your `.env` file
4. Configure `CHANNEL_USERNAME` with your channel username
5. The bot will automatically approve all join requests

### Monitoring Bot Activity

```bash
# View real-time logs
tail -f logs/bot.log

# With Docker
docker-compose logs -f
```

### Managing Users

Use the `/users` command (admin only) to see:
- Total user count
- Recent user activity
- Join statistics

## Deployment Options 🚀

### Local Development

```bash
python run.py
```

### Production with systemd

1. Create a systemd service file:
   ```bash
   sudo nano /etc/systemd/system/telegram-bot.service
   ```

2. Add service configuration:
   ```ini
   [Unit]
   Description=Telegram Channel Bot
   After=network.target

   [Service]
   Type=simple
   User=your-user
   WorkingDirectory=/path/to/joinjesterbot
   ExecStart=/path/to/joinjesterbot/venv/bin/python run.py
   Restart=always

   [Install]
   WantedBy=multi-user.target
   ```

3. Enable and start:
   ```bash
   sudo systemctl enable telegram-bot
   sudo systemctl start telegram-bot
   ```

### Docker Production

```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f

# Update
docker-compose pull && docker-compose up -d
```

## Troubleshooting 🔧

### Common Issues

1. **Bot not responding:**
   - Check if bot token is correct
   - Verify bot is added to channel as admin
   - Check logs for error messages

2. **Join requests not auto-approved:**
   - Ensure `AUTO_APPROVE_REQUESTS=true`
   - Verify bot has "Invite users via link" permission
   - Check if bot is admin in the channel

3. **Permission errors:**
   - Make sure scripts are executable: `chmod +x scripts/*.sh`
   - Check file permissions for data and logs directories

### Logs

Check logs for detailed error information:
```bash
# Local installation
tail -f logs/bot.log

# Docker installation
docker-compose logs -f telegram-bot
```

## Contributing 🤝

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License 📄

This project is licensed under the MIT License - see the LICENSE file for details.

## Support 💬

If you encounter any issues or have questions:

1. Check the troubleshooting section above
2. Review the logs for error messages
3. Open an issue on GitHub with detailed information

## Security 🔒

- Never commit your `.env` file or bot token to version control
- Use environment variables for all sensitive configuration
- Regularly update dependencies for security patches
- Monitor bot logs for suspicious activity

---

**Happy botting! 🎉**
