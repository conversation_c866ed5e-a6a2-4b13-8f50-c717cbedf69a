#!/bin/bash
# Validation script for Telegram Channel Bot

set -e

echo "🔍 Validating Telegram Channel Bot setup..."

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check Python version
check_python_version() {
    python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
    required_version="3.8"
    
    if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
        echo "❌ Python 3.8 or higher is required. Found: $python_version"
        return 1
    else
        echo "✅ Python version check passed: $python_version"
        return 0
    fi
}

# Check system requirements
echo "📋 Checking system requirements..."

if command_exists python3; then
    check_python_version
else
    echo "❌ Python 3 is not installed"
    exit 1
fi

if command_exists pip3; then
    echo "✅ pip3 is available"
else
    echo "❌ pip3 is not installed"
    exit 1
fi

# Check project structure
echo "📁 Checking project structure..."

required_files=(
    "bot/__init__.py"
    "bot/main.py"
    "bot/handlers.py"
    "bot/database.py"
    "config/settings.py"
    "requirements.txt"
    "run.py"
    ".env.example"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file exists"
    else
        echo "❌ $file is missing"
        exit 1
    fi
done

required_dirs=(
    "bot"
    "config"
    "scripts"
    "tests"
)

for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/ directory exists"
    else
        echo "❌ $dir/ directory is missing"
        exit 1
    fi
done

# Check if .env file exists
if [ -f ".env" ]; then
    echo "✅ .env file exists"
    
    # Check if bot token is configured
    if grep -q "TELEGRAM_BOT_TOKEN=" .env && ! grep -q "TELEGRAM_BOT_TOKEN=$" .env; then
        echo "✅ Bot token is configured"
    else
        echo "⚠️  Bot token is not configured in .env file"
    fi
    
    # Check if channel username is configured
    if grep -q "CHANNEL_USERNAME=" .env && ! grep -q "CHANNEL_USERNAME=your_channel_username" .env; then
        echo "✅ Channel username is configured"
    else
        echo "⚠️  Channel username is not configured in .env file"
    fi
else
    echo "⚠️  .env file does not exist (copy from .env.example)"
fi

# Check virtual environment
if [ -d "venv" ]; then
    echo "✅ Virtual environment exists"
    
    # Check if virtual environment is activated
    if [[ "$VIRTUAL_ENV" != "" ]]; then
        echo "✅ Virtual environment is activated"
    else
        echo "⚠️  Virtual environment is not activated"
        echo "   Run: source venv/bin/activate"
    fi
else
    echo "⚠️  Virtual environment does not exist"
    echo "   Run: python3 -m venv venv"
fi

# Check dependencies if virtual environment exists
if [ -d "venv" ]; then
    echo "📦 Checking dependencies..."
    
    # Activate virtual environment for dependency check
    source venv/bin/activate 2>/dev/null || true
    
    # Check if python-telegram-bot is installed
    if python -c "import telegram" 2>/dev/null; then
        echo "✅ python-telegram-bot is installed"
    else
        echo "❌ python-telegram-bot is not installed"
        echo "   Run: pip install -r requirements.txt"
    fi
    
    # Check if python-dotenv is installed
    if python -c "import dotenv" 2>/dev/null; then
        echo "✅ python-dotenv is installed"
    else
        echo "⚠️  python-dotenv is not installed (optional)"
    fi
fi

# Check Docker setup (if Docker is available)
if command_exists docker; then
    echo "🐳 Docker is available"
    
    if command_exists docker-compose; then
        echo "✅ Docker Compose is available"
    else
        echo "⚠️  Docker Compose is not available"
    fi
    
    if [ -f "Dockerfile" ]; then
        echo "✅ Dockerfile exists"
    else
        echo "❌ Dockerfile is missing"
    fi
    
    if [ -f "docker-compose.yml" ]; then
        echo "✅ docker-compose.yml exists"
    else
        echo "❌ docker-compose.yml is missing"
    fi
else
    echo "⚠️  Docker is not available (optional for deployment)"
fi

# Check script permissions
echo "🔧 Checking script permissions..."

scripts=(
    "scripts/install.sh"
    "scripts/start.sh"
    "scripts/deploy.sh"
    "scripts/test.sh"
    "scripts/validate.sh"
)

for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo "✅ $script is executable"
        else
            echo "⚠️  $script is not executable"
            echo "   Run: chmod +x $script"
        fi
    else
        echo "❌ $script is missing"
    fi
done

# Test basic imports
echo "🧪 Testing basic imports..."

if [ -d "venv" ]; then
    source venv/bin/activate 2>/dev/null || true
fi

python3 -c "
import sys
import os
sys.path.insert(0, '.')

try:
    from config.settings import config
    print('✅ Configuration module imports successfully')
except ImportError as e:
    print(f'❌ Configuration import error: {e}')
    sys.exit(1)

try:
    from bot.database import BotDatabase
    print('✅ Database module imports successfully')
except ImportError as e:
    print(f'❌ Database import error: {e}')
    sys.exit(1)

try:
    from bot.handlers import BotHandlers
    print('✅ Handlers module imports successfully')
except ImportError as e:
    print(f'❌ Handlers import error: {e}')
    sys.exit(1)
"

echo ""
echo "🎉 Validation completed!"
echo ""
echo "📋 Summary:"
echo "✅ = Ready"
echo "⚠️  = Warning (may need attention)"
echo "❌ = Error (must be fixed)"
echo ""
echo "📖 For setup instructions, see README.md"
