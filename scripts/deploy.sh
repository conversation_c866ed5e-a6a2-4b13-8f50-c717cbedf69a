#!/bin/bash
# Deployment script for Telegram Channel Bot

set -e

echo "🚀 Deploying Telegram Channel Bot..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed!"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if docker-compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed!"
    echo "Please install Docker Compose first: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "Please copy .env.example to .env and configure your settings."
    exit 1
fi

# Check if bot token is configured
if ! grep -q "TELEGRAM_BOT_TOKEN=" .env || grep -q "TELEGRAM_BOT_TOKEN=$" .env; then
    echo "❌ Bot token not configured in .env file!"
    echo "Please set TELEGRAM_BOT_TOKEN in your .env file."
    exit 1
fi

# Create directories
mkdir -p data logs

# Build and start the container
echo "🔨 Building Docker image..."
docker-compose build

echo "🚀 Starting bot container..."
docker-compose up -d

echo "✅ Bot deployed successfully!"
echo ""
echo "📋 Useful commands:"
echo "  View logs: docker-compose logs -f"
echo "  Stop bot: docker-compose down"
echo "  Restart bot: docker-compose restart"
echo "  Update bot: docker-compose pull && docker-compose up -d"
