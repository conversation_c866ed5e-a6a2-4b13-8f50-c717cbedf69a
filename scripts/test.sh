#!/bin/bash
# Test script for Telegram Channel Bot

set -e

echo "🧪 Running tests for Telegram Channel Bot..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run ./scripts/install.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check if pytest is installed
if ! python -c "import pytest" 2>/dev/null; then
    echo "📦 Installing test dependencies..."
    pip install pytest pytest-asyncio
fi

# Create test directories if they don't exist
mkdir -p data logs

# Run tests
echo "🔍 Running unit tests..."
python -m pytest tests/ -v

# Run configuration validation
echo "🔧 Testing configuration..."
python -c "
import sys
import os
sys.path.insert(0, '.')
try:
    from config.settings import config
    print('✅ Configuration loaded successfully')
    
    # Test with minimal required config
    os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
    os.environ['CHANNEL_USERNAME'] = 'test_channel'
    
    if config.validate():
        print('✅ Configuration validation passed')
    else:
        print('❌ Configuration validation failed')
        sys.exit(1)
        
except Exception as e:
    print(f'❌ Configuration error: {e}')
    sys.exit(1)
"

# Test database operations
echo "💾 Testing database operations..."
python -c "
import sys
import tempfile
import os
sys.path.insert(0, '.')

try:
    from bot.database import BotDatabase
    
    # Create temporary database
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
        temp_file = f.name
    
    db = BotDatabase(temp_file)
    
    # Test basic operations
    db.add_user(123456789, 'testuser', 'Test', 'User', 'direct')
    user = db.get_user(123456789)
    stats = db.get_stats()
    
    assert user is not None
    assert stats['total_users'] == 1
    assert stats['total_joins'] == 1
    
    # Cleanup
    os.unlink(temp_file)
    
    print('✅ Database operations test passed')
    
except Exception as e:
    print(f'❌ Database test error: {e}')
    sys.exit(1)
"

# Test import structure
echo "📦 Testing import structure..."
python -c "
import sys
sys.path.insert(0, '.')

try:
    from bot.main import main
    from bot.handlers import BotHandlers
    from bot.database import BotDatabase
    from config.settings import config
    
    print('✅ All modules imported successfully')
    
except ImportError as e:
    print(f'❌ Import error: {e}')
    sys.exit(1)
"

# Test Docker build (if Docker is available)
if command -v docker &> /dev/null; then
    echo "🐳 Testing Docker build..."
    if docker build -t telegram-channel-bot-test . > /dev/null 2>&1; then
        echo "✅ Docker build successful"
        docker rmi telegram-channel-bot-test > /dev/null 2>&1
    else
        echo "⚠️  Docker build failed (this may be expected if dependencies are missing)"
    fi
else
    echo "⚠️  Docker not available, skipping Docker build test"
fi

echo ""
echo "✅ All tests completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your .env file with real bot token"
echo "2. Run: ./scripts/start.sh"
