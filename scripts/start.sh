#!/bin/bash
# Start script for Telegram Channel Bot

set -e

echo "🚀 Starting Telegram Channel Bot..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "Please copy .env.example to .env and configure your settings."
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "❌ Virtual environment not found!"
    echo "Please run ./scripts/install.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check if bot token is configured
if ! grep -q "TELEGRAM_BOT_TOKEN=" .env || grep -q "TELEGRAM_BOT_TOKEN=$" .env; then
    echo "❌ Bot token not configured in .env file!"
    echo "Please set TELEGRAM_BOT_TOKEN in your .env file."
    exit 1
fi

# Create directories if they don't exist
mkdir -p data logs

echo "✅ Starting bot..."
python run.py
