# Deployment Guide 🚀

This guide covers various deployment options for the Telegram Channel Bot.

## Prerequisites

- Python 3.8+ (for local deployment)
- Docker & Docker Compose (for containerized deployment)
- A Telegram Bot Token from [@BotFather](https://t.me/BotFather)
- Channel admin permissions for your bot

## Local Development Deployment

### Quick Setup

```bash
# Clone and setup
git clone <your-repo>
cd joinjesterbot
./scripts/install.sh

# Configure
cp .env.example .env
# Edit .env with your settings

# Start
./scripts/start.sh
```

### Manual Setup

```bash
# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Configure environment
cp .env.example .env
# Edit .env file

# Run
python run.py
```

## Production Deployment

### Option 1: Docker Compose (Recommended)

```bash
# Setup
cp .env.example .env
# Configure .env file

# Deploy
./scripts/deploy.sh

# Monitor
docker-compose logs -f
```

### Option 2: Systemd Service

1. **Create service file:**
```bash
sudo nano /etc/systemd/system/telegram-channel-bot.service
```

2. **Add configuration:**
```ini
[Unit]
Description=Telegram Channel Bot
After=network.target

[Service]
Type=simple
User=botuser
Group=botuser
WorkingDirectory=/opt/telegram-channel-bot
Environment=PATH=/opt/telegram-channel-bot/venv/bin
ExecStart=/opt/telegram-channel-bot/venv/bin/python run.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

3. **Enable and start:**
```bash
sudo systemctl daemon-reload
sudo systemctl enable telegram-channel-bot
sudo systemctl start telegram-channel-bot
sudo systemctl status telegram-channel-bot
```

### Option 3: PM2 (Node.js Process Manager)

```bash
# Install PM2
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'telegram-channel-bot',
    script: 'run.py',
    interpreter: 'python3',
    cwd: '/path/to/joinjesterbot',
    env: {
      PYTHONPATH: '/path/to/joinjesterbot'
    },
    restart_delay: 5000,
    max_restarts: 10
  }]
}
EOF

# Start with PM2
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

## Cloud Deployment

### Heroku

1. **Create Heroku app:**
```bash
heroku create your-bot-name
```

2. **Set environment variables:**
```bash
heroku config:set TELEGRAM_BOT_TOKEN=your_token
heroku config:set CHANNEL_USERNAME=your_channel
```

3. **Create Procfile:**
```bash
echo "worker: python run.py" > Procfile
```

4. **Deploy:**
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### DigitalOcean Droplet

1. **Create droplet** (Ubuntu 20.04+)

2. **Setup server:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install python3 python3-pip python3-venv git -y

# Clone repository
git clone <your-repo>
cd joinjesterbot

# Setup
./scripts/install.sh
```

3. **Configure systemd service** (see Option 2 above)

### AWS EC2

Similar to DigitalOcean, but:

1. Launch EC2 instance (Amazon Linux 2 or Ubuntu)
2. Configure security groups (no inbound ports needed for bot)
3. Follow DigitalOcean setup steps

### Google Cloud Platform

1. **Create VM instance**
2. **Setup similar to DigitalOcean**
3. **Optional: Use Cloud Run for serverless deployment**

## Environment Configuration

### Required Variables

```env
TELEGRAM_BOT_TOKEN=your_bot_token
CHANNEL_USERNAME=your_channel_username
```

### Optional Variables

```env
CHANNEL_ID=your_channel_id
ADMIN_USER_IDS=123456789,987654321
AUTO_APPROVE_REQUESTS=true
SEND_WELCOME_MESSAGES=true
LOG_LEVEL=INFO
DATA_FILE=data/bot_data.json
LOG_FILE=logs/bot.log
MAX_REQUESTS_PER_MINUTE=30
```

## Monitoring and Maintenance

### Log Monitoring

```bash
# Local logs
tail -f logs/bot.log

# Docker logs
docker-compose logs -f

# Systemd logs
sudo journalctl -u telegram-channel-bot -f
```

### Health Checks

```bash
# Check if bot is running
ps aux | grep python | grep run.py

# Check Docker container
docker-compose ps

# Check systemd service
sudo systemctl status telegram-channel-bot
```

### Backup Data

```bash
# Manual backup
cp data/bot_data.json data/bot_data.json.backup.$(date +%Y%m%d_%H%M%S)

# Automated backup (add to crontab)
0 2 * * * cd /path/to/joinjesterbot && cp data/bot_data.json data/bot_data.json.backup.$(date +\%Y\%m\%d_\%H\%M\%S)
```

## Security Considerations

### File Permissions

```bash
# Secure configuration files
chmod 600 .env
chmod 700 data/
chmod 700 logs/
```

### Firewall Configuration

```bash
# Ubuntu/Debian
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh

# No inbound ports needed for Telegram bot
```

### Regular Updates

```bash
# Update dependencies
pip install --upgrade -r requirements.txt

# Update system packages
sudo apt update && sudo apt upgrade -y
```

## Troubleshooting

### Common Issues

1. **Bot not starting:**
   - Check bot token validity
   - Verify Python version (3.8+)
   - Check file permissions

2. **Permission denied errors:**
   ```bash
   chmod +x scripts/*.sh
   sudo chown -R $USER:$USER .
   ```

3. **Docker issues:**
   ```bash
   # Rebuild container
   docker-compose down
   docker-compose build --no-cache
   docker-compose up -d
   ```

4. **Memory issues:**
   - Monitor with `htop` or `docker stats`
   - Consider upgrading server resources

### Performance Optimization

1. **Reduce log verbosity:**
   ```env
   LOG_LEVEL=WARNING
   ```

2. **Optimize Docker:**
   ```bash
   # Use multi-stage builds
   # Minimize image size
   # Use .dockerignore
   ```

3. **Database optimization:**
   - Regular data cleanup
   - Consider external database for large deployments

## Scaling

### Horizontal Scaling

For high-traffic channels:

1. **Use external database** (PostgreSQL, MongoDB)
2. **Implement Redis for caching**
3. **Use load balancer** for multiple bot instances
4. **Consider webhook mode** instead of polling

### Vertical Scaling

- Increase server resources (CPU, RAM)
- Optimize Python code
- Use faster storage (SSD)

---

**Need help? Check the main README.md or open an issue!**
