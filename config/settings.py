"""
Configuration settings for the Telegram Channel Bot
"""

import os
from typing import List

# Load environment variables from .env file if it exists
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # python-dotenv not installed, skip loading .env file
    pass

class Config:
    """Bot configuration class"""
    
    # Bot Token (required)
    BOT_TOKEN: str = os.getenv('TELEGRAM_BOT_TOKEN', '**********************************************')
    
    # Channel settings
    CHANNEL_USERNAME: str = os.getenv('CHANNEL_USERNAME', 'your_channel_username')
    CHANNEL_ID: str = os.getenv('CHANNEL_ID', '')  # Optional: Channel ID for direct operations
    
    # File paths
    DATA_FILE: str = os.getenv('DATA_FILE', 'data/bot_data.json')
    LOG_FILE: str = os.getenv('LOG_FILE', 'logs/bot.log')
    
    # Admin settings
    ADMIN_USER_IDS: List[int] = []
    if os.getenv('ADMIN_USER_IDS'):
        try:
            ADMIN_USER_IDS = [int(uid.strip()) for uid in os.getenv('ADMIN_USER_IDS').split(',') if uid.strip()]
        except ValueError:
            ADMIN_USER_IDS = []
    
    # Bot behavior settings
    AUTO_APPROVE_REQUESTS: bool = os.getenv('AUTO_APPROVE_REQUESTS', 'true').lower() == 'true'
    SEND_WELCOME_MESSAGES: bool = os.getenv('SEND_WELCOME_MESSAGES', 'true').lower() == 'true'
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO').upper()
    
    # Rate limiting
    MAX_REQUESTS_PER_MINUTE: int = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '30'))
    
    # Messages
    WELCOME_MESSAGE: str = os.getenv('WELCOME_MESSAGE', """
🎉 Welcome {first_name}!

I'm your channel assistant bot. Here's what I can do:

🔹 Auto-accept join requests to the channel
🔹 Help users join the channel directly
🔹 Keep track of all members
🔹 Provide channel statistics

Use /help to see all available commands.
    """)
    
    JOIN_MESSAGE: str = os.getenv('JOIN_MESSAGE', """
🎯 Ready to join our channel, {first_name}?

Click the button below to join directly, or you can:
1. Search for our channel in Telegram
2. Send a join request (I'll auto-approve it!)
3. Enjoy the content!

You're now registered in our system! 🎉
    """)
    
    @classmethod
    def validate(cls) -> bool:
        """Validate configuration"""
        if not cls.BOT_TOKEN or cls.BOT_TOKEN == 'your_bot_token_here':
            raise ValueError("BOT_TOKEN is required and must be set")
        
        if not cls.CHANNEL_USERNAME or cls.CHANNEL_USERNAME == 'your_channel_username':
            print("Warning: CHANNEL_USERNAME should be set to your actual channel username")
        
        return True

# Create config instance
config = Config()
