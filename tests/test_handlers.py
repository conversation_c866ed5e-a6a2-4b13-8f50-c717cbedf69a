"""
Tests for the handlers module
"""

import pytest
import asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
import tempfile
import os

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.handlers import BotHandlers
from bot.database import BotDatabase

class TestBotHandlers:
    """Test cases for BotHandlers class"""
    
    def setup_method(self):
        """Setup test environment"""
        # Create temporary database
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()
        
        # Mock database
        with patch('bot.handlers.db') as mock_db:
            self.mock_db = mock_db
            self.mock_db.add_user.return_value = True
            self.mock_db.get_stats.return_value = {
                'total_users': 10,
                'total_joins': 15,
                'auto_accepts': 8,
                'direct_joins': 7,
                'bot_started': '2023-01-01T00:00:00'
            }
            self.mock_db.is_admin.return_value = False
    
    def teardown_method(self):
        """Cleanup"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def create_mock_update(self, user_id=123456789, username="testuser", first_name="Test"):
        """Create a mock update object"""
        update = MagicMock()
        update.effective_user.id = user_id
        update.effective_user.username = username
        update.effective_user.first_name = first_name
        update.effective_user.last_name = "User"
        update.message.reply_text = AsyncMock()
        return update
    
    def create_mock_context(self):
        """Create a mock context object"""
        context = MagicMock()
        context.bot.approve_chat_join_request = AsyncMock()
        context.bot.send_message = AsyncMock()
        return context
    
    @pytest.mark.asyncio
    async def test_start_command(self):
        """Test /start command handler"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        await BotHandlers.start_command(update, context)
        
        # Verify user was added to database
        self.mock_db.add_user.assert_called_once_with(
            123456789, "testuser", "Test", "User", "direct"
        )
        
        # Verify reply was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "Welcome Test!" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_help_command(self):
        """Test /help command handler"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        await BotHandlers.help_command(update, context)
        
        # Verify reply was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "Bot Commands:" in call_args[0][0]
        assert "/start" in call_args[0][0]
        assert "/help" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_stats_command(self):
        """Test /stats command handler"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        await BotHandlers.stats_command(update, context)
        
        # Verify stats were retrieved
        self.mock_db.get_stats.assert_called_once()
        
        # Verify reply was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "Channel Statistics:" in call_args[0][0]
        assert "Total Users: 10" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_join_command(self):
        """Test /join command handler"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        await BotHandlers.join_command(update, context)
        
        # Verify user was added to database
        self.mock_db.add_user.assert_called_once_with(
            123456789, "testuser", "Test", "User", "direct"
        )
        
        # Verify reply was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "Ready to join our channel, Test?" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_users_command_non_admin(self):
        """Test /users command for non-admin user"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        # Mock non-admin user
        self.mock_db.is_admin.return_value = False
        
        await BotHandlers.users_command(update, context)
        
        # Verify admin check was called
        self.mock_db.is_admin.assert_called_once_with(123456789)
        
        # Verify access denied message
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "only available to administrators" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_users_command_admin(self):
        """Test /users command for admin user"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        # Mock admin user
        self.mock_db.is_admin.return_value = True
        self.mock_db.get_all_users.return_value = {
            "123456789": {
                "username": "testuser",
                "first_name": "Test",
                "join_type": "direct",
                "interaction_count": 5,
                "last_interaction": "2023-01-01T12:00:00"
            }
        }
        
        await BotHandlers.users_command(update, context)
        
        # Verify admin check was called
        self.mock_db.is_admin.assert_called_once_with(123456789)
        
        # Verify user data was retrieved
        self.mock_db.get_all_users.assert_called_once()
        
        # Verify reply was sent
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "User Management (Admin)" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_backup_command_non_admin(self):
        """Test /backup command for non-admin user"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        # Mock non-admin user
        self.mock_db.is_admin.return_value = False
        
        await BotHandlers.backup_command(update, context)
        
        # Verify admin check was called
        self.mock_db.is_admin.assert_called_once_with(123456789)
        
        # Verify access denied message
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "only available to administrators" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_backup_command_admin(self):
        """Test /backup command for admin user"""
        update = self.create_mock_update()
        context = self.create_mock_context()
        
        # Mock admin user and successful backup
        self.mock_db.is_admin.return_value = True
        self.mock_db.backup_data.return_value = "backup_file.json"
        
        await BotHandlers.backup_command(update, context)
        
        # Verify admin check was called
        self.mock_db.is_admin.assert_called_once_with(123456789)
        
        # Verify backup was created
        self.mock_db.backup_data.assert_called_once()
        
        # Verify success message
        update.message.reply_text.assert_called_once()
        call_args = update.message.reply_text.call_args
        assert "backup created" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_handle_join_request(self):
        """Test join request handler"""
        update = MagicMock()
        context = self.create_mock_context()
        
        # Mock join request
        update.chat_join_request.from_user.id = 123456789
        update.chat_join_request.from_user.username = "testuser"
        update.chat_join_request.from_user.first_name = "Test"
        update.chat_join_request.from_user.last_name = "User"
        update.chat_join_request.chat.id = -1001234567890
        
        with patch('config.settings.config.AUTO_APPROVE_REQUESTS', True):
            with patch('config.settings.config.SEND_WELCOME_MESSAGES', True):
                await BotHandlers.handle_join_request(update, context)
        
        # Verify join request was approved
        context.bot.approve_chat_join_request.assert_called_once_with(
            chat_id=-1001234567890,
            user_id=123456789
        )
        
        # Verify user was added to database
        self.mock_db.add_user.assert_called_once_with(
            123456789, "testuser", "Test", "User", "auto_accept"
        )
        
        # Verify welcome message was sent
        context.bot.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_button_callback_join_channel(self):
        """Test button callback for join channel"""
        update = MagicMock()
        context = self.create_mock_context()
        
        update.callback_query.data = "join_channel"
        update.callback_query.answer = AsyncMock()
        update.callback_query.edit_message_text = AsyncMock()
        
        await BotHandlers.button_callback(update, context)
        
        # Verify callback was answered
        update.callback_query.answer.assert_called_once()
        
        # Verify message was edited
        update.callback_query.edit_message_text.assert_called_once()
        call_args = update.callback_query.edit_message_text.call_args
        assert "Click the button below to join" in call_args[0][0]
    
    @pytest.mark.asyncio
    async def test_button_callback_stats(self):
        """Test button callback for stats"""
        update = MagicMock()
        context = self.create_mock_context()
        
        update.callback_query.data = "stats"
        update.callback_query.answer = AsyncMock()
        update.callback_query.edit_message_text = AsyncMock()
        
        await BotHandlers.button_callback(update, context)
        
        # Verify callback was answered
        update.callback_query.answer.assert_called_once()
        
        # Verify stats were retrieved
        self.mock_db.get_stats.assert_called_once()
        
        # Verify message was edited
        update.callback_query.edit_message_text.assert_called_once()
        call_args = update.callback_query.edit_message_text.call_args
        assert "Channel Statistics:" in call_args[0][0]

if __name__ == '__main__':
    pytest.main([__file__])
