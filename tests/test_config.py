"""
Tests for the configuration module
"""

import pytest
import os
import tempfile

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import Config

class TestConfig:
    """Test cases for Config class"""
    
    def setup_method(self):
        """Setup test environment"""
        # Store original environment variables
        self.original_env = {}
        env_vars = [
            'TELEGRAM_BOT_TOKEN', 'CHANNEL_USERNAME', 'CHANNEL_ID',
            'ADMIN_USER_IDS', 'AUTO_APPROVE_REQUESTS', 'SEND_WELCOME_MESSAGES',
            'LOG_LEVEL', 'DATA_FILE', 'LOG_FILE', 'MAX_REQUESTS_PER_MINUTE'
        ]
        
        for var in env_vars:
            self.original_env[var] = os.environ.get(var)
            if var in os.environ:
                del os.environ[var]
    
    def teardown_method(self):
        """Restore original environment"""
        for var, value in self.original_env.items():
            if value is not None:
                os.environ[var] = value
            elif var in os.environ:
                del os.environ[var]
    
    def test_default_values(self):
        """Test default configuration values"""
        config = Config()
        
        assert config.BOT_TOKEN == '7436544351:AAGU_Mml5eH80TVqi2-U94o2zndm_3ObvD4'
        assert config.CHANNEL_USERNAME == 'your_channel_username'
        assert config.CHANNEL_ID == ''
        assert config.ADMIN_USER_IDS == []
        assert config.AUTO_APPROVE_REQUESTS is True
        assert config.SEND_WELCOME_MESSAGES is True
        assert config.LOG_LEVEL == 'INFO'
        assert config.DATA_FILE == 'data/bot_data.json'
        assert config.LOG_FILE == 'logs/bot.log'
        assert config.MAX_REQUESTS_PER_MINUTE == 30
    
    def test_environment_override(self):
        """Test environment variable override"""
        # Set environment variables
        os.environ['TELEGRAM_BOT_TOKEN'] = 'test_token'
        os.environ['CHANNEL_USERNAME'] = 'test_channel'
        os.environ['CHANNEL_ID'] = '-1001234567890'
        os.environ['ADMIN_USER_IDS'] = '123456789,987654321'
        os.environ['AUTO_APPROVE_REQUESTS'] = 'false'
        os.environ['SEND_WELCOME_MESSAGES'] = 'false'
        os.environ['LOG_LEVEL'] = 'DEBUG'
        os.environ['DATA_FILE'] = 'custom/data.json'
        os.environ['LOG_FILE'] = 'custom/bot.log'
        os.environ['MAX_REQUESTS_PER_MINUTE'] = '60'
        
        config = Config()
        
        assert config.BOT_TOKEN == 'test_token'
        assert config.CHANNEL_USERNAME == 'test_channel'
        assert config.CHANNEL_ID == '-1001234567890'
        assert config.ADMIN_USER_IDS == [123456789, 987654321]
        assert config.AUTO_APPROVE_REQUESTS is False
        assert config.SEND_WELCOME_MESSAGES is False
        assert config.LOG_LEVEL == 'DEBUG'
        assert config.DATA_FILE == 'custom/data.json'
        assert config.LOG_FILE == 'custom/bot.log'
        assert config.MAX_REQUESTS_PER_MINUTE == 60
    
    def test_admin_user_ids_parsing(self):
        """Test admin user IDs parsing"""
        # Test empty string
        os.environ['ADMIN_USER_IDS'] = ''
        config = Config()
        assert config.ADMIN_USER_IDS == []
        
        # Test single ID
        os.environ['ADMIN_USER_IDS'] = '123456789'
        config = Config()
        assert config.ADMIN_USER_IDS == [123456789]
        
        # Test multiple IDs
        os.environ['ADMIN_USER_IDS'] = '123456789,987654321,555666777'
        config = Config()
        assert config.ADMIN_USER_IDS == [123456789, 987654321, 555666777]
        
        # Test with spaces
        os.environ['ADMIN_USER_IDS'] = '123456789, 987654321 , 555666777'
        config = Config()
        assert config.ADMIN_USER_IDS == [123456789, 987654321, 555666777]
        
        # Test invalid format (should result in empty list)
        os.environ['ADMIN_USER_IDS'] = 'invalid,123abc,456'
        config = Config()
        assert config.ADMIN_USER_IDS == []
    
    def test_boolean_parsing(self):
        """Test boolean environment variable parsing"""
        # Test true values
        for true_value in ['true', 'True', 'TRUE', '1', 'yes', 'Yes']:
            os.environ['AUTO_APPROVE_REQUESTS'] = true_value
            config = Config()
            assert config.AUTO_APPROVE_REQUESTS is True
        
        # Test false values
        for false_value in ['false', 'False', 'FALSE', '0', 'no', 'No', '']:
            os.environ['AUTO_APPROVE_REQUESTS'] = false_value
            config = Config()
            assert config.AUTO_APPROVE_REQUESTS is False
    
    def test_validation_success(self):
        """Test successful configuration validation"""
        os.environ['TELEGRAM_BOT_TOKEN'] = 'valid_token'
        os.environ['CHANNEL_USERNAME'] = 'valid_channel'
        
        config = Config()
        assert config.validate() is True
    
    def test_validation_failure(self):
        """Test configuration validation failure"""
        # Test missing bot token
        os.environ['TELEGRAM_BOT_TOKEN'] = ''
        config = Config()
        
        with pytest.raises(ValueError, match="BOT_TOKEN is required"):
            config.validate()
        
        # Test default bot token
        os.environ['TELEGRAM_BOT_TOKEN'] = 'your_bot_token_here'
        config = Config()
        
        with pytest.raises(ValueError, match="BOT_TOKEN is required"):
            config.validate()
    
    def test_message_formatting(self):
        """Test message template formatting"""
        config = Config()
        
        # Test welcome message formatting
        welcome = config.WELCOME_MESSAGE.format(first_name="TestUser")
        assert "TestUser" in welcome
        assert "🎉 Welcome TestUser!" in welcome
        
        # Test join message formatting
        join = config.JOIN_MESSAGE.format(first_name="TestUser")
        assert "TestUser" in join
        assert "🎯 Ready to join our channel, TestUser?" in join

if __name__ == '__main__':
    pytest.main([__file__])
