"""
Tests for the database module
"""

import pytest
import tempfile
import os
import json
from datetime import datetime

import sys
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bot.database import BotDatabase

class TestBotDatabase:
    """Test cases for BotDatabase class"""
    
    def setup_method(self):
        """Setup test database with temporary file"""
        self.temp_file = tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json')
        self.temp_file.close()
        self.db = BotDatabase(self.temp_file.name)
    
    def teardown_method(self):
        """Cleanup temporary file"""
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_initialization(self):
        """Test database initialization"""
        assert self.db.data is not None
        assert 'users' in self.db.data
        assert 'stats' in self.db.data
        assert isinstance(self.db.data['users'], dict)
        assert isinstance(self.db.data['stats'], dict)
    
    def test_add_user(self):
        """Test adding a user"""
        user_id = 123456789
        username = "testuser"
        first_name = "Test"
        last_name = "User"
        
        result = self.db.add_user(user_id, username, first_name, last_name, "direct")
        
        assert result is True
        assert str(user_id) in self.db.data['users']
        
        user_data = self.db.data['users'][str(user_id)]
        assert user_data['username'] == username
        assert user_data['first_name'] == first_name
        assert user_data['last_name'] == last_name
        assert user_data['join_type'] == "direct"
        assert 'join_date' in user_data
        assert 'last_interaction' in user_data
        assert user_data['interaction_count'] == 1
    
    def test_add_existing_user(self):
        """Test adding an existing user (should update)"""
        user_id = 123456789
        username = "testuser"
        first_name = "Test"
        
        # Add user first time
        self.db.add_user(user_id, username, first_name, None, "direct")
        original_join_date = self.db.data['users'][str(user_id)]['join_date']
        
        # Add same user again
        self.db.add_user(user_id, username, first_name, None, "auto_accept")
        
        user_data = self.db.data['users'][str(user_id)]
        assert user_data['join_date'] == original_join_date  # Should not change
        assert user_data['interaction_count'] == 2  # Should increment
        assert user_data['join_type'] == "auto_accept"  # Should update
    
    def test_get_user(self):
        """Test getting user data"""
        user_id = 123456789
        username = "testuser"
        first_name = "Test"
        
        # User doesn't exist
        assert self.db.get_user(user_id) is None
        
        # Add user and get
        self.db.add_user(user_id, username, first_name, None, "direct")
        user_data = self.db.get_user(user_id)
        
        assert user_data is not None
        assert user_data['username'] == username
        assert user_data['first_name'] == first_name
    
    def test_get_stats(self):
        """Test getting statistics"""
        stats = self.db.get_stats()
        
        assert 'total_users' in stats
        assert 'total_joins' in stats
        assert 'auto_accepts' in stats
        assert 'direct_joins' in stats
        assert stats['total_users'] == 0
        
        # Add some users
        self.db.add_user(1, "user1", "User1", None, "direct")
        self.db.add_user(2, "user2", "User2", None, "auto_accept")
        
        stats = self.db.get_stats()
        assert stats['total_users'] == 2
        assert stats['total_joins'] == 2
        assert stats['auto_accepts'] == 1
        assert stats['direct_joins'] == 1
    
    def test_save_and_load_data(self):
        """Test saving and loading data"""
        user_id = 123456789
        username = "testuser"
        first_name = "Test"
        
        # Add user and save
        self.db.add_user(user_id, username, first_name, None, "direct")
        assert self.db.save_data() is True
        
        # Create new database instance with same file
        new_db = BotDatabase(self.temp_file.name)
        
        # Check if data was loaded correctly
        assert str(user_id) in new_db.data['users']
        user_data = new_db.get_user(user_id)
        assert user_data['username'] == username
        assert user_data['first_name'] == first_name
    
    def test_backup_data(self):
        """Test data backup functionality"""
        # Add some data
        self.db.add_user(123, "user", "Test", None, "direct")
        
        # Create backup
        backup_file = self.db.backup_data()
        
        assert backup_file != ""
        assert os.path.exists(backup_file)
        
        # Verify backup content
        with open(backup_file, 'r') as f:
            backup_data = json.load(f)
        
        assert '123' in backup_data['users']
        
        # Cleanup
        os.unlink(backup_file)
    
    def test_is_admin(self):
        """Test admin check functionality"""
        # Mock admin user IDs
        import config.settings
        original_admin_ids = config.settings.config.ADMIN_USER_IDS
        config.settings.config.ADMIN_USER_IDS = [123456789]
        
        try:
            assert self.db.is_admin(123456789) is True
            assert self.db.is_admin(987654321) is False
        finally:
            # Restore original admin IDs
            config.settings.config.ADMIN_USER_IDS = original_admin_ids

if __name__ == '__main__':
    pytest.main([__file__])
