#!/usr/bin/env python3
"""
Telegram Channel Bot - Legacy Entry Point
This file is kept for backward compatibility.
For the new modular version, use: python -m bot.main
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import and run the new main function
from bot.main import main

if __name__ == '__main__':
    print("⚠️  Using legacy entry point. Consider using: python -m bot.main")
    main()


