"""
Database management for the Telegram Channel Bot
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from config.settings import config

logger = logging.getLogger(__name__)

class BotDatabase:
    """Simple JSON-based database for bot data"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or config.DATA_FILE
        self.data = self.load_data()
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(self.data_file), exist_ok=True)
    
    def load_data(self) -> Dict[str, Any]:
        """Load data from JSON file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # Ensure required structure exists
                    if 'users' not in data:
                        data['users'] = {}
                    if 'stats' not in data:
                        data['stats'] = {
                            'total_joins': 0,
                            'auto_accepts': 0,
                            'direct_joins': 0,
                            'bot_started': datetime.now().isoformat()
                        }
                    return data
            else:
                return {
                    'users': {},
                    'stats': {
                        'total_joins': 0,
                        'auto_accepts': 0,
                        'direct_joins': 0,
                        'bot_started': datetime.now().isoformat()
                    }
                }
        except Exception as e:
            logger.error(f"Error loading data from {self.data_file}: {e}")
            return {
                'users': {},
                'stats': {
                    'total_joins': 0,
                    'auto_accepts': 0,
                    'direct_joins': 0,
                    'bot_started': datetime.now().isoformat()
                }
            }
    
    def save_data(self) -> bool:
        """Save data to JSON file"""
        try:
            # Create backup of existing data
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # Save new data
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            logger.error(f"Error saving data to {self.data_file}: {e}")
            return False
    
    def add_user(self, user_id: int, username: str, first_name: str, 
                 last_name: str = None, join_type: str = "direct") -> bool:
        """Add or update user data"""
        try:
            user_id_str = str(user_id)
            
            # Check if user already exists
            existing_user = self.data['users'].get(user_id_str)
            is_new_user = existing_user is None
            
            self.data['users'][user_id_str] = {
                'user_id': user_id,
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'join_date': existing_user.get('join_date') if existing_user else datetime.now().isoformat(),
                'join_type': join_type,
                'last_interaction': datetime.now().isoformat(),
                'interaction_count': existing_user.get('interaction_count', 0) + 1 if existing_user else 1
            }
            
            # Update stats only for new users
            if is_new_user:
                self.data['stats']['total_joins'] += 1
                if join_type == "auto_accept":
                    self.data['stats']['auto_accepts'] += 1
                elif join_type == "direct":
                    self.data['stats']['direct_joins'] += 1
            
            return self.save_data()
        except Exception as e:
            logger.error(f"Error adding user {user_id}: {e}")
            return False
    
    def get_user(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user data by ID"""
        return self.data['users'].get(str(user_id))
    
    def get_stats(self) -> Dict[str, Any]:
        """Get bot statistics"""
        stats = self.data['stats'].copy()
        stats['total_users'] = len(self.data['users'])
        return stats
    
    def get_all_users(self) -> Dict[str, Any]:
        """Get all users data"""
        return self.data['users']
    
    def is_admin(self, user_id: int) -> bool:
        """Check if user is admin"""
        return user_id in config.ADMIN_USER_IDS
    
    def backup_data(self) -> str:
        """Create a timestamped backup of the data"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"{self.data_file}.backup_{timestamp}"
            
            with open(self.data_file, 'r', encoding='utf-8') as src:
                with open(backup_file, 'w', encoding='utf-8') as dst:
                    dst.write(src.read())
            
            logger.info(f"Data backed up to {backup_file}")
            return backup_file
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return ""

# Global database instance
db = BotDatabase()
