"""
Command and event handlers for the Telegram Channel Bot
"""

import logging
from datetime import datetime
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from config.settings import config
from bot.database import db

logger = logging.getLogger(__name__)

class BotHandlers:
    """Collection of bot command and event handlers"""
    
    @staticmethod
    async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        try:
            user = update.effective_user
            
            # Add user to database
            db.add_user(
                user.id,
                user.username,
                user.first_name,
                user.last_name,
                "direct"
            )
            
            welcome_text = config.WELCOME_MESSAGE.format(
                first_name=user.first_name or "User"
            )
            
            # Customize button text based on channel type
            join_button_text = "🔐 Join Private Channel" if config.IS_PRIVATE_CHANNEL else "📢 Join Channel"

            keyboard = [
                [InlineKeyboardButton(join_button_text, callback_data="join_channel")],
                [InlineKeyboardButton("📊 Stats", callback_data="stats")],
                [InlineKeyboardButton("ℹ️ Help", callback_data="help")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(welcome_text, reply_markup=reply_markup)
            logger.info(f"Start command handled for user {user.first_name} (@{user.username})")
            
        except Exception as e:
            logger.error(f"Error in start command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")
    
    @staticmethod
    async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            help_text = """
🤖 **Bot Commands:**

/start - Start the bot and get welcome message
/help - Show this help message
/stats - Show channel statistics
/join - Get channel join link
/users - Show total users (admin only)
/backup - Create data backup (admin only)

**Features:**
✅ Auto-accepts all join requests
✅ Remembers all users who interact
✅ Provides channel statistics
✅ Easy channel joining

**How it works:**
1. Users can request to join the channel
2. Bot automatically approves all requests
3. Users can also join directly using the bot
4. All interactions are logged and remembered

**Admin Commands:**
/users - View user statistics
/backup - Create data backup
/broadcast - Send message to all users
            """
            
            user = update.effective_user
            if db.is_admin(user.id):
                help_text += "\n\n🔧 **You have admin privileges!**"
            
            await update.message.reply_text(help_text, parse_mode='Markdown')
            logger.info(f"Help command handled for user {user.first_name}")
            
        except Exception as e:
            logger.error(f"Error in help command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")
    
    @staticmethod
    async def stats_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stats command"""
        try:
            stats = db.get_stats()
            
            stats_text = f"""
📊 **Channel Statistics:**

👥 Total Users: {stats['total_users']}
🔄 Total Joins: {stats['total_joins']}
✅ Auto-Accepts: {stats['auto_accepts']}
🤝 Direct Joins: {stats['direct_joins']}

📅 Bot Started: {datetime.fromisoformat(stats['bot_started']).strftime('%Y-%m-%d %H:%M:%S')}
🕐 Current Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await update.message.reply_text(stats_text, parse_mode='Markdown')
            logger.info(f"Stats command handled for user {update.effective_user.first_name}")
            
        except Exception as e:
            logger.error(f"Error in stats command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")
    
    @staticmethod
    async def join_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /join command"""
        try:
            user = update.effective_user

            # Add user to database
            db.add_user(
                user.id,
                user.username,
                user.first_name,
                user.last_name,
                "direct"
            )

            # Handle private vs public channels
            if config.IS_PRIVATE_CHANNEL:
                # For private channels, use invite link
                keyboard = [
                    [InlineKeyboardButton("🔐 Join Private Channel", url=config.CHANNEL_INVITE_LINK)]
                ]
                join_text = config.PRIVATE_CHANNEL_JOIN_MESSAGE.format(
                    first_name=user.first_name or "User"
                )
            else:
                # For public channels, use username
                channel_url = f"https://t.me/{config.CHANNEL_USERNAME}"
                keyboard = [
                    [InlineKeyboardButton("📢 Join Channel Now", url=channel_url)]
                ]
                join_text = config.JOIN_MESSAGE.format(
                    first_name=user.first_name or "User"
                )

            reply_markup = InlineKeyboardMarkup(keyboard)
            await update.message.reply_text(join_text, reply_markup=reply_markup)
            logger.info(f"Join command handled for user {user.first_name} (private: {config.IS_PRIVATE_CHANNEL})")

        except Exception as e:
            logger.error(f"Error in join command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")
    
    @staticmethod
    async def users_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /users command (admin only)"""
        try:
            user = update.effective_user
            
            if not db.is_admin(user.id):
                await update.message.reply_text("❌ This command is only available to administrators.")
                return
            
            all_users = db.get_all_users()
            stats = db.get_stats()
            
            # Create summary
            recent_users = []
            for user_data in all_users.values():
                if user_data.get('last_interaction'):
                    try:
                        last_interaction = datetime.fromisoformat(user_data['last_interaction'])
                        recent_users.append((user_data, last_interaction))
                    except:
                        pass
            
            # Sort by last interaction
            recent_users.sort(key=lambda x: x[1], reverse=True)
            
            users_text = f"""
👥 **User Management (Admin)**

📊 **Summary:**
• Total Users: {stats['total_users']}
• Auto-Accepts: {stats['auto_accepts']}
• Direct Joins: {stats['direct_joins']}

📋 **Recent Users (Last 10):**
            """
            
            for i, (user_data, last_interaction) in enumerate(recent_users[:10]):
                username = user_data.get('username', 'N/A')
                first_name = user_data.get('first_name', 'Unknown')
                join_type = user_data.get('join_type', 'unknown')
                interaction_count = user_data.get('interaction_count', 1)
                
                users_text += f"\n{i+1}. {first_name} (@{username})"
                users_text += f"\n   Type: {join_type} | Interactions: {interaction_count}"
                users_text += f"\n   Last seen: {last_interaction.strftime('%Y-%m-%d %H:%M')}\n"
            
            await update.message.reply_text(users_text, parse_mode='Markdown')
            logger.info(f"Users command handled for admin {user.first_name}")
            
        except Exception as e:
            logger.error(f"Error in users command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")
    
    @staticmethod
    async def backup_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /backup command (admin only)"""
        try:
            user = update.effective_user
            
            if not db.is_admin(user.id):
                await update.message.reply_text("❌ This command is only available to administrators.")
                return
            
            backup_file = db.backup_data()
            if backup_file:
                await update.message.reply_text(f"✅ Data backup created: {backup_file}")
            else:
                await update.message.reply_text("❌ Failed to create backup.")
            
            logger.info(f"Backup command handled for admin {user.first_name}")
            
        except Exception as e:
            logger.error(f"Error in backup command: {e}")
            await update.message.reply_text("Sorry, something went wrong. Please try again later.")

    @staticmethod
    async def handle_join_request(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Auto-approve all join requests"""
        try:
            if not config.AUTO_APPROVE_REQUESTS:
                logger.info("Auto-approval is disabled")
                return

            chat_join_request = update.chat_join_request
            user = chat_join_request.from_user
            chat = chat_join_request.chat

            # Auto-approve the request
            await context.bot.approve_chat_join_request(
                chat_id=chat.id,
                user_id=user.id
            )

            # Add user to database
            db.add_user(
                user.id,
                user.username,
                user.first_name,
                user.last_name,
                "auto_accept"
            )

            logger.info(f"Auto-approved join request from {user.first_name} (@{user.username})")

            # Send welcome message to the user if enabled
            if config.SEND_WELCOME_MESSAGES:
                welcome_msg = f"""
🎉 Welcome to the channel, {user.first_name}!

Your join request has been automatically approved.
Enjoy the content and feel free to interact!

Use /help to see what I can do for you.
                """

                try:
                    await context.bot.send_message(
                        chat_id=user.id,
                        text=welcome_msg
                    )
                except TelegramError as e:
                    logger.warning(f"Could not send welcome message to {user.first_name}: {e}")

        except Exception as e:
            logger.error(f"Error handling join request: {e}")

    @staticmethod
    async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        try:
            query = update.callback_query
            await query.answer()

            if query.data == "join_channel":
                # Handle private vs public channels
                if config.IS_PRIVATE_CHANNEL:
                    keyboard = [
                        [InlineKeyboardButton("🔐 Join Private Channel", url=config.CHANNEL_INVITE_LINK)]
                    ]
                    message_text = "🔐 Click the button below to join our exclusive private channel!\n\nI'll automatically approve your request once you click join."
                else:
                    channel_url = f"https://t.me/{config.CHANNEL_USERNAME}"
                    keyboard = [
                        [InlineKeyboardButton("📢 Join Channel Now", url=channel_url)]
                    ]
                    message_text = "🎯 Click the button below to join our channel!"

                reply_markup = InlineKeyboardMarkup(keyboard)
                await query.edit_message_text(message_text, reply_markup=reply_markup)

            elif query.data == "stats":
                stats = db.get_stats()

                stats_text = f"""
📊 **Channel Statistics:**

👥 Total Users: {stats['total_users']}
🔄 Total Joins: {stats['total_joins']}
✅ Auto-Accepts: {stats['auto_accepts']}
🤝 Direct Joins: {stats['direct_joins']}
                """
                await query.edit_message_text(stats_text, parse_mode='Markdown')

            elif query.data == "help":
                await query.edit_message_text(
                    "Use /help command to see detailed help information!"
                )

            logger.info(f"Button callback handled: {query.data}")

        except Exception as e:
            logger.error(f"Error in button callback: {e}")
            try:
                await query.edit_message_text("Sorry, something went wrong. Please try again later.")
            except:
                pass
