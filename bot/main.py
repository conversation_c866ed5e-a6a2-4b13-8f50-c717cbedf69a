#!/usr/bin/env python3
"""
Main entry point for the Telegram Channel Bot
"""

import logging
import os
import sys
from telegram import Update
from telegram.ext import (
    Application,
    CommandHandler,
    CallbackQueryHandler,
    ChatJoinRequestHandler,
    MessageHandler,
    filters
)

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.settings import config
from bot.handlers import BotHandlers
from bot.database import db

def setup_logging():
    """Configure logging for the bot"""
    # Create logs directory if it doesn't exist
    os.makedirs(os.path.dirname(config.LOG_FILE), exist_ok=True)
    
    # Configure logging
    logging.basicConfig(
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        level=getattr(logging, config.LOG_LEVEL),
        handlers=[
            logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set specific loggers
    logging.getLogger('httpx').setLevel(logging.WARNING)
    logging.getLogger('telegram').setLevel(logging.INFO)
    
    return logging.getLogger(__name__)

def create_application():
    """Create and configure the bot application"""
    # Validate configuration
    config.validate()
    
    # Create application
    application = Application.builder().token(config.BOT_TOKEN).build()
    
    # Add command handlers
    application.add_handler(CommandHandler("start", BotHandlers.start_command))
    application.add_handler(CommandHandler("help", BotHandlers.help_command))
    application.add_handler(CommandHandler("stats", BotHandlers.stats_command))
    application.add_handler(CommandHandler("join", BotHandlers.join_command))
    application.add_handler(CommandHandler("users", BotHandlers.users_command))
    application.add_handler(CommandHandler("backup", BotHandlers.backup_command))
    
    # Add event handlers
    application.add_handler(ChatJoinRequestHandler(BotHandlers.handle_join_request))
    application.add_handler(CallbackQueryHandler(BotHandlers.button_callback))
    
    # Add error handler
    async def error_handler(update: object, context) -> None:
        """Log errors caused by Updates."""
        logger.error(f"Exception while handling an update: {context.error}")
        
        # Try to send error message to user if possible
        if isinstance(update, Update) and update.effective_message:
            try:
                await update.effective_message.reply_text(
                    "Sorry, an error occurred while processing your request. Please try again later."
                )
            except Exception:
                pass
    
    application.add_error_handler(error_handler)
    
    return application

def main():
    """Main function to start the bot"""
    global logger
    logger = setup_logging()
    
    try:
        logger.info("Starting Telegram Channel Bot...")
        logger.info(f"Bot Token: {config.BOT_TOKEN[:10]}...")
        logger.info(f"Channel Username: @{config.CHANNEL_USERNAME}")
        logger.info(f"Auto-approve requests: {config.AUTO_APPROVE_REQUESTS}")
        logger.info(f"Send welcome messages: {config.SEND_WELCOME_MESSAGES}")
        logger.info(f"Admin users: {len(config.ADMIN_USER_IDS)}")
        
        # Create application
        application = create_application()
        
        # Start the bot
        logger.info("Bot is starting... Press Ctrl+C to stop.")
        application.run_polling(
            allowed_updates=Update.ALL_TYPES,
            drop_pending_updates=True
        )
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
